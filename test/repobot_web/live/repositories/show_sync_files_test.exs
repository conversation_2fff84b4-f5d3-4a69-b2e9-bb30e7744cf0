defmodule RepobotWeb.Live.Repositories.ShowSyncFilesTest do
  use RepobotWeb.ConnCase, async: false
  use Repobot.Test.Fixtures

  import Mox
  import Phoenix.LiveViewTest

  setup :set_mox_from_context
  setup :verify_on_exit!

  describe "synced files display in repository tree" do
    @tag load_repos: "solnic", only: ["solnic/drops"]
    test "shows synced files with indigo background and badge", %{
      conn: conn,
      user: user,
      repositories: [repository]
    } do
      # Create repository files
      create_repository_files(repository, [
        %{path: "README.md", name: "README.md", type: "file", size: 1024, sha: "abc123"},
        %{path: "src/main.js", name: "main.js", type: "file", size: 512, sha: "def456"},
        %{path: "package.json", name: "package.json", type: "file", size: 256, sha: "ghi789"}
      ])

      # Create a source file and associate it with the repository
      source_file =
        create_source_file(%{
          name: "README.md",
          content: "# Test Repository",
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, _} = Repobot.Repositories.add_source_file(repository, source_file)

      # Load the repository page
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/repositories/#{repository}")

      # Check that the synced file has the indigo background
      assert html =~ "data-imported=\"true\""
      assert html =~ "bg-indigo-50"
      assert html =~ "Synced"

      # Check that non-synced files don't have the background
      assert html =~ "data-imported=\"false\""
    end

    test "shows folders containing synced files with indigo background and badge", %{
      conn: conn
    } do
      # Create a user first
      user = create_user(%{login: "test-owner"})

      # Create a repository
      repository =
        create_repository(%{
          name: "test-repo",
          owner: "test-owner",
          full_name: "test-owner/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create repository files including nested files
      create_repository_files(repository, [
        %{path: "README.md", name: "README.md", type: "file", size: 1024, sha: "abc123"},
        %{path: "src/main.js", name: "main.js", type: "file", size: 512, sha: "def456"},
        %{path: "src/utils.js", name: "utils.js", type: "file", size: 256, sha: "ghi789"},
        %{path: "docs/guide.md", name: "guide.md", type: "file", size: 128, sha: "jkl012"}
      ])

      # Create a source file that targets a file in the src folder
      source_file =
        create_source_file(%{
          name: "main.js",
          content: "console.log('hello');",
          target_path: "src/main.js",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, _} = Repobot.Repositories.add_source_file(repository, source_file)

      # Load the repository page
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/repositories/#{repository}")

      # Check that the src folder has the sync indication
      assert html =~ "data-folder-synced=\"true\""
      assert html =~ "Contains synced files"

      # Navigate into the src folder to see the synced file
      view |> element("button[phx-value-path=\"/src\"]") |> render_click()

      updated_html = render(view)
      assert updated_html =~ "data-imported=\"true\""
      assert updated_html =~ "Synced"
    end

    test "works with template repositories using imported_files", %{conn: conn} do
      # Create a user first
      user = create_user(%{login: "template-owner"})

      # Create a template repository
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: "template-owner",
          full_name: "template-owner/template-repo",
          template: true,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create repository files
      create_repository_files(template_repo, [
        %{path: "template.md", name: "template.md", type: "file", size: 1024, sha: "abc123"}
      ])

      # Create an imported source file (created from this template)
      source_file =
        create_source_file(%{
          name: "template.md",
          content: "# Template File",
          target_path: "template.md",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Load the template repository page
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/repositories/#{template_repo}")

      # Check that the imported file is marked as synced
      assert html =~ "data-imported=\"true\""
      assert html =~ "Synced"
    end
  end
end
