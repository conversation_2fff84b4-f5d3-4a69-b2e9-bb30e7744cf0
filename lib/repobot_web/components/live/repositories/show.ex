defmodule RepobotWeb.Live.Repositories.Show do
  use RepobotWeb, :live_view
  require Logger

  import RepobotWeb.UI.Components
  alias Repobot.Repositories

  def mount(%{"id" => id}, _session, socket) do
    repository =
      Repositories.get_repository!(id)
      |> Repobot.Repo.preload([:imported_files, :source_files, :files])

    # Subscribe to repository file changes if connected
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_files")
    end

    {:ok,
     socket
     |> assign(:repository, repository)
     |> assign(:files, repository.files)
     |> assign(:current_path, "/")
     |> assign(:page_title, repository.full_name)
     |> assign(:is_refreshing, false)}
  end

  def handle_event("navigate", %{"path" => path}, socket) do
    # Ensure we always get "/" for the root path and normalize other paths
    current_path =
      cond do
        path == "" -> "/"
        path == "/" -> "/"
        String.starts_with?(path, "/") -> path
        true -> "/" <> path
      end

    {:noreply,
     socket
     |> assign(:current_path, current_path)}
  end

  def handle_event("refresh_tree", _params, socket) do
    # First set is_refreshing to true and return immediately to update the UI
    Process.send_after(self(), :perform_refresh, 10)
    {:noreply, assign(socket, :is_refreshing, true)}
  end

  def handle_event("import_file", %{"path" => path}, socket) do
    case github_api().get_file_content(
           github_api().client(socket.assigns.current_user),
           socket.assigns.repository.owner,
           socket.assigns.repository.name,
           path
         ) do
      {:ok, content, _response} ->
        repository =
          socket.assigns.repository |> Repobot.Repo.preload([:folder, :template_folders])

        # If the repository is a template, get all repositories from all associated folders
        {repositories_to_associate, folders_to_associate} =
          if repository.template do
            # Get repositories from both the primary folder and template folders
            folders = [repository.folder | repository.template_folders] |> Enum.reject(&is_nil/1)

            # Get all repositories from all folders
            repositories =
              folders
              |> Enum.flat_map(fn folder ->
                Repobot.Repo.preload(folder, :repositories).repositories
              end)
              |> Enum.uniq_by(& &1.id)

            {repositories, folders}
          else
            # For regular repositories, associate with the repository's folder if it exists
            folders = if repository.folder, do: [repository.folder], else: []
            {[repository], folders}
          end

        # Create source file with content and associate with repositories
        attrs = %{
          name: Path.basename(path),
          content: content,
          target_path: path,
          organization_id: socket.assigns.current_organization.id,
          source_repository_id: repository.id,
          read_only: repository.template,
          user_id: socket.assigns.current_user.id
        }

        case Repobot.SourceFiles.import_file(
               attrs,
               repositories_to_associate
             ) do
          {:ok, source_file} ->
            # Associate the source file with all folders
            Enum.each(folders_to_associate, fn folder ->
              case Repobot.SourceFiles.add_source_file_to_folder(source_file, folder) do
                {:ok, _} ->
                  :ok

                _ ->
                  Logger.warning(
                    "Failed to associate source file #{source_file.id} with folder #{folder.id}"
                  )
              end
            end)

            # Reload repository to get updated associations
            repository =
              socket.assigns.repository
              |> Repobot.Repo.preload([:source_files, :imported_files], force: true)

            {:noreply,
             socket
             |> assign(:repository, repository)
             |> put_flash(:info, "File imported successfully")}

          {:error, reason} ->
            Logger.error("Failed to import file: #{inspect(reason)}")

            {:noreply,
             socket
             |> put_flash(:error, "Failed to import file: #{reason}")}
        end

      {:error, reason} ->
        Logger.error("Failed to fetch file content: #{inspect(reason)}")

        {:noreply,
         socket
         |> put_flash(:error, "Failed to import file: #{reason}")}
    end
  end

  def handle_event("remove_source_file", %{"source-file-id" => source_file_id}, socket) do
    repository = socket.assigns.repository
    source_file = Repobot.SourceFiles.get_source_file!(source_file_id)

    case Repobot.Repositories.remove_source_file(repository, source_file) do
      {1, nil} ->
        # Reload repository to get updated associations
        repository =
          Repobot.Repositories.get_repository!(repository.id)
          |> Repobot.Repo.preload([:folder, :source_files])

        {:noreply,
         socket
         |> assign(:repository, repository)
         |> put_flash(:info, "Source file #{source_file.name} removed from repository")}

      {0, nil} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to remove source file #{source_file.name} from repository")}
    end
  end

  def handle_event("delete_repository", _params, socket) do
    repository = socket.assigns.repository

    Logger.info("Repository deletion requested from UI",
      repository_id: repository.id,
      full_name: repository.full_name,
      github_id: get_in(repository.data, ["id"])
    )

    case Repositories.delete_repository_with_cleanup(repository) do
      {:ok, _deleted_repository} ->
        Logger.info("Repository successfully deleted from UI",
          repository_id: repository.id,
          full_name: repository.full_name,
          github_id: get_in(repository.data, ["id"])
        )

        {:noreply,
         socket
         |> put_flash(:info, "Repository deleted successfully")
         |> push_navigate(to: ~p"/repositories")}

      {:error, reason} ->
        Logger.error("Failed to delete repository from UI",
          repository_id: repository.id,
          full_name: repository.full_name,
          github_id: get_in(repository.data, ["id"]),
          error: inspect(reason)
        )

        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete repository")}
    end
  end

  # Add a new handle_info callback to handle the actual refresh
  def handle_info(:perform_refresh, socket) do
    case Repositories.refresh_repository_files!(
           socket.assigns.repository.id,
           socket.assigns.current_user
         ) do
      {:error, reason} ->
        {:noreply,
         socket
         |> assign(:is_refreshing, false)
         |> put_flash(:error, "Failed to refresh repository files: #{reason}")}

      {:ok, repository} ->
        repository =
          repository
          |> Repobot.Repo.preload([:imported_files, :source_files, :files], force: true)

        {:noreply,
         socket
         |> assign(:repository, repository)
         |> assign(:files, repository.files)
         |> assign(:is_refreshing, false)
         |> put_flash(:info, "Repository files refreshed successfully")}
    end
  end

  # Handle repository files updated events from push webhooks
  def handle_info({:repository_files_updated, repository_id, _metadata}, socket) do
    # Only refresh if this event is for the current repository
    if socket.assigns.repository.id == repository_id do
      # Refresh the repository with updated files
      repository =
        Repositories.get_repository!(repository_id)
        |> Repobot.Repo.preload([:imported_files, :source_files, :files], force: true)

      {:noreply,
       socket
       |> assign(:repository, repository)
       |> assign(:files, repository.files)
       |> put_flash(:info, "Repository files updated automatically")}
    else
      {:noreply, socket}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-8">
        <nav class="mb-6">
          <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
            <li>
              <.link navigate={~p"/repositories"} class="hover:text-indigo-600">
                Repositories
              </.link>
            </li>
            <li>•</li>
            <li class="font-medium text-slate-900">{@repository.full_name}</li>
          </ol>
        </nav>
        <div class="flex justify-between items-center mb-8">
          <div>
            <div class="flex items-center gap-2">
              <h1 class="text-2xl font-semibold text-slate-900">{@repository.full_name}</h1>
              <%= if @repository.template do %>
                <.badge variant="accent">
                  <.icon name="hero-arrow-path-rounded-square" class="w-4 h-4 mr-1" /> Template
                </.badge>
              <% end %>
            </div>
            <p class="mt-2 text-sm text-slate-600">
              {@repository.data["description"] || "No description provided"}
            </p>
          </div>
          <div class="flex items-center gap-2">
            <.btn href={~p"/repositories/#{@repository}/edit"} variant="soft">
              <.icon name="hero-pencil" class="h-4 w-4 mr-2" /> Edit
            </.btn>
            <.btn
              phx-click="delete_repository"
              data-confirm={"Are you sure you want to delete #{@repository.full_name}? This action cannot be undone and will remove all associated data."}
              variant="error"
            >
              <.icon name="hero-trash" class="h-4 w-4 mr-2" /> Delete
            </.btn>
            <.btn href={@repository.data["html_url"]} target="_blank" variant="secondary">
              <.icon name="hero-arrow-top-right-on-square" class="h-4 w-4 mr-2" /> View on GitHub
            </.btn>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2 space-y-6">
          <.content>
            <:header>Repository Files</:header>
            <:actions>
              <.btn phx-click="refresh_tree" variant="soft">
                <.icon name="hero-arrow-path" class="h-4 w-4 mr-2" /> Refresh Files
              </.btn>
            </:actions>
            <:body>
              <div class="px-6 py-4">
                <nav class="flex items-center space-x-2 text-sm text-slate-500 mb-4">
                  <.link
                    :if={@current_path != "/"}
                    phx-click="navigate"
                    phx-value-path={
                      if(Path.dirname(@current_path) == "",
                        do: "/",
                        else: Path.dirname(@current_path)
                      )
                    }
                    class="hover:text-indigo-600"
                  >
                    ..
                  </.link>
                  <span :if={@current_path != "/"}>/</span>
                  <%= for part <- path_parts(@current_path) do %>
                    <.link
                      :if={part.path != @current_path}
                      phx-click="navigate"
                      phx-value-path={part.path}
                      class="hover:text-indigo-600"
                    >
                      {part.name}
                    </.link>
                    <span :if={part.path != @current_path}>/</span>
                    <span :if={part.path == @current_path} class="font-medium text-slate-900">
                      {part.name}
                    </span>
                  <% end %>
                </nav>
                <%= if @is_refreshing do %>
                  <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span class="ml-3 text-sm text-slate-600">Refreshing files...</span>
                  </div>
                <% else %>
                  <%= for item <- filter_tree_by_path(@files, @current_path) do %>
                    <.render_tree_item item={item} repository={@repository} />
                  <% end %>
                <% end %>
              </div>
            </:body>
          </.content>
        </div>

        <div class="space-y-6">
          <.content>
            <:header>Repository Details</:header>
            <:body>
              <div class="px-6 py-5 space-y-4">
                <div>
                  <dt class="text-sm font-medium text-slate-500">Default Branch</dt>
                  <dd class="mt-1 text-sm text-slate-900">{@repository.data["default_branch"]}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-slate-500">Language</dt>
                  <dd class="mt-1 text-sm text-slate-900">
                    {@repository.data["language"] || "Not specified"}
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-slate-500">Created</dt>
                  <dd class="mt-1 text-sm text-slate-900">
                    <%= if created_at = @repository.data["created_at"] do %>
                      <%= with {:ok, datetime, _} <- DateTime.from_iso8601(created_at) do %>
                        {Calendar.strftime(datetime, "%Y-%m-%d")}
                      <% end %>
                    <% else %>
                      Not available
                    <% end %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-slate-500">Last Push</dt>
                  <dd class="mt-1 text-sm text-slate-900">
                    <%= if pushed_at = @repository.data["pushed_at"] do %>
                      <%= with {:ok, datetime, _} <- DateTime.from_iso8601(pushed_at) do %>
                        {Calendar.strftime(datetime, "%Y-%m-%d %H:%M:%S")}
                      <% end %>
                    <% else %>
                      Not available
                    <% end %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-slate-500">License</dt>
                  <dd class="mt-1 text-sm text-slate-900">
                    <%= if license = @repository.data["license"] do %>
                      {license["name"]}
                    <% else %>
                      Not specified
                    <% end %>
                  </dd>
                </div>
              </div>
            </:body>
          </.content>

          <.content>
            <:header>Statistics</:header>
            <:body>
              <div class="px-6 py-5 space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <dt class="text-sm font-medium text-slate-500">Stars</dt>
                    <dd class="mt-1 text-sm text-slate-900">
                      {@repository.data["stargazers_count"]}
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-slate-500">Forks</dt>
                    <dd class="mt-1 text-sm text-slate-900">{@repository.data["forks_count"]}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-slate-500">Open Issues</dt>
                    <dd class="mt-1 text-sm text-slate-900">
                      {@repository.data["open_issues_count"]}
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-slate-500">Watchers</dt>
                    <dd class="mt-1 text-sm text-slate-900">
                      {@repository.data["watchers_count"]}
                    </dd>
                  </div>
                </div>
              </div>
            </:body>
          </.content>
        </div>
      </div>
    </div>
    """
  end

  defp filter_tree_by_path(tree, "/") do
    # For root, get all unique top-level entries
    tree
    |> Enum.reduce(%{}, fn item, acc ->
      parts = String.split(item.path, "/")

      case parts do
        # Single part means it's a file in root
        [_name] ->
          Map.put_new(acc, item.path, %{
            "name" => item.name,
            "path" => item.path,
            "type" => item.type,
            "size" => item.size
          })

        # Multiple parts means it's in a directory
        [dir | _rest] ->
          # Check if we already have an explicit directory entry for this path
          dir_path = dir
          existing_dir = Enum.find(tree, fn t -> t.path == dir_path && t.type == "dir" end)

          if existing_dir do
            # Use the explicit directory entry
            Map.put_new(acc, dir_path, %{
              "name" => existing_dir.name,
              "path" => "/" <> dir_path,
              "type" => "dir",
              "size" => existing_dir.size
            })
          else
            # Create virtual directory entry
            Map.put_new(acc, dir_path, %{
              "name" => dir,
              "path" => "/" <> dir,
              "type" => "dir",
              "size" => 0
            })
          end
      end
    end)
    |> Map.values()
    |> Enum.sort_by(&{if(&1["type"] == "dir", do: 0, else: 1), String.downcase(&1["name"])})
  end

  defp filter_tree_by_path(tree, path) do
    # For non-root paths, filter items that belong to the current directory
    path = String.trim_leading(path, "/")
    path_parts = String.split(path, "/")
    path_depth = length(path_parts)

    # Find explicit directory entries at the current depth
    explicit_dirs =
      tree
      |> Enum.filter(fn item ->
        item_parts = String.split(item.path, "/")

        item.type == "dir" and length(item_parts) == path_depth + 1 and
          Enum.take(item_parts, path_depth) == path_parts
      end)

    # Find files at the current depth
    files_at_depth =
      tree
      |> Enum.filter(fn item ->
        item_parts = String.split(item.path, "/")

        item.type == "file" and length(item_parts) == path_depth + 1 and
          Enum.take(item_parts, path_depth) == path_parts
      end)

    # Find files that are deeper (to create virtual directories)
    files_deeper =
      tree
      |> Enum.filter(fn item ->
        item_parts = String.split(item.path, "/")

        item.type == "file" and length(item_parts) > path_depth + 1 and
          Enum.take(item_parts, path_depth) == path_parts
      end)

    # Create entries for explicit directories
    dir_entries =
      explicit_dirs
      |> Enum.map(fn dir ->
        {dir.path,
         %{
           "name" => dir.name,
           "path" => "/" <> dir.path,
           "type" => "dir",
           "size" => dir.size
         }}
      end)
      |> Map.new()

    # Create entries for files at current depth
    file_entries =
      files_at_depth
      |> Enum.map(fn file ->
        {file.path,
         %{
           "name" => file.name,
           "path" => file.path,
           "type" => file.type,
           "size" => file.size
         }}
      end)
      |> Map.new()

    # Create virtual directory entries for deeper files (only if no explicit dir exists)
    virtual_dir_entries =
      files_deeper
      |> Enum.reduce(%{}, fn file, acc ->
        parts = String.split(file.path, "/")
        dir_path = Enum.take(parts, path_depth + 1) |> Enum.join("/")
        current_path = "/" <> dir_path
        current_name = Enum.at(parts, path_depth)

        # Only create virtual directory if no explicit directory exists
        if not Map.has_key?(dir_entries, dir_path) do
          Map.put_new(acc, dir_path, %{
            "name" => current_name,
            "path" => current_path,
            "type" => "dir",
            "size" => 0
          })
        else
          acc
        end
      end)

    # Combine all entries
    Map.merge(dir_entries, file_entries)
    |> Map.merge(virtual_dir_entries)
    |> Map.values()
    |> Enum.sort_by(&{if(&1["type"] == "dir", do: 0, else: 1), String.downcase(&1["name"])})
  end

  defp path_parts("/"), do: []

  defp path_parts(path) do
    path
    |> String.trim("/")
    |> String.split("/")
    |> Enum.reduce([], fn part, acc ->
      case acc do
        [] ->
          [%{name: part, path: "/" <> part}]

        parts ->
          parts ++
            [
              %{
                name: part,
                path:
                  "/" <> (List.last(parts).path |> String.trim_leading("/") |> Path.join(part))
              }
            ]
      end
    end)
  end

  def is_imported?(item, repository) do
    item["type"] == "file" && find_source_files(item, repository) != []
  end

  def find_source_files(item, repository) do
    item_path = item["path"]

    # Get the appropriate source files based on repository type
    source_files =
      if repository.template do
        repository.imported_files || []
      else
        repository.source_files || []
      end

    Enum.filter(source_files, fn source_file ->
      # For template files, we need to handle the .liquid extension
      if source_file.is_template && String.ends_with?(item_path, ".liquid") do
        # If the repository file has .liquid extension and source file is a template,
        # check if the target_path matches the item path without .liquid
        source_file.target_path == String.replace_suffix(item_path, ".liquid", "")
      else
        # For non-template files, direct match
        source_file.target_path == item_path
      end
    end)
  end

  def folder_contains_synced_files?(folder_path, repository) do
    # Get all source files for this repository
    source_files =
      if repository.template do
        repository.imported_files || []
      else
        repository.source_files || []
      end

    # Normalize folder path - ensure it starts with / and ends with /
    normalized_folder_path =
      folder_path
      |> String.trim_trailing("/")
      |> then(fn path ->
        if String.starts_with?(path, "/") do
          path <> "/"
        else
          "/" <> path <> "/"
        end
      end)

    # Check if any source file's target_path starts with the folder path
    Enum.any?(source_files, fn source_file ->
      target_path =
        if String.starts_with?(source_file.target_path, "/") do
          source_file.target_path
        else
          "/" <> source_file.target_path
        end

      String.starts_with?(target_path, normalized_folder_path)
    end)
  end

  defp render_tree_item(assigns) do
    ~H"""
    <% is_imported = is_imported?(@item, @repository) %>
    <% folder_has_synced =
      @item["type"] == "dir" && folder_contains_synced_files?(@item["path"], @repository) %>
    <% has_sync_indication = is_imported || folder_has_synced %>
    <div
      class={"flex items-center justify-between py-2 #{if has_sync_indication, do: "bg-indigo-50 -mx-6 px-6"}"}
      data-filename={@item["name"]}
      data-path={@item["path"]}
      data-imported={to_string(is_imported)}
      data-folder-synced={to_string(folder_has_synced)}
    >
      <div class="flex items-center">
        <span class="mr-2">
          <%= if @item["type"] == "dir" do %>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class={"h-5 w-5 #{if folder_has_synced, do: "text-indigo-400", else: "text-slate-400"}"}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
            </svg>
          <% else %>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class={"h-5 w-5 #{if is_imported, do: "text-indigo-400", else: "text-slate-400"}"}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                clip-rule="evenodd"
              />
            </svg>
          <% end %>
        </span>
        <%= if @item["type"] == "dir" do %>
          <button
            phx-click="navigate"
            phx-value-path={@item["path"]}
            class="text-sm font-medium text-indigo-600 hover:text-indigo-900 hover:underline"
          >
            {@item["name"]}/
            <%= if folder_has_synced do %>
              <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                Contains synced files
              </span>
            <% end %>
          </button>
        <% else %>
          <span class={"text-sm font-medium #{if is_imported, do: "text-indigo-900", else: "text-slate-900"}"}>
            {@item["name"]}
            <%= if is_imported do %>
              <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                Synced
              </span>
            <% end %>
          </span>
        <% end %>
      </div>
      <div class="flex items-center space-x-4">
        <span class="text-xs text-slate-500">
          <%= if @item["type"] == "file" do %>
            {Number.Bytes.to_string(@item["size"])}
          <% end %>
        </span>
        <%= if @item["type"] == "file" do %>
          <%= if is_imported do %>
            <.btn
              :for={source_file <- find_source_files(@item, @repository)}
              href={~p"/source-files/#{source_file.id}"}
              data-phx-link="redirect"
              data-phx-link-state="push"
              variant="soft"
              size="xs"
            >
              View Source File
            </.btn>
          <% else %>
            <%= if @repository.template do %>
              <.btn
                type="button"
                phx-click="import_file"
                phx-value-path={@item["path"]}
                phx-disable-with="Importing..."
                variant="primary"
                size="xs"
              >
                Import
              </.btn>
            <% end %>
          <% end %>
        <% end %>
      </div>
    </div>
    """
  end

  defp github_api do
    Application.get_env(:repobot, :github_api)
  end
end
